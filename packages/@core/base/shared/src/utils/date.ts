import dayjs from 'dayjs';

export function formatDate(time?: Date | number | string, format = 'YYYY-MM-DD', emptyString = ''): string {
  if (!time) {
    return emptyString;
  }
  try {
    const date = dayjs(time);
    if (!date.isValid()) {
      throw new Error('Invalid date');
    }
    return date.format(format);
  } catch (error) {
    console.error(`Error formatting date: ${error}`);
    return time as string;
  }
}

export function formatDateTime(time: Date | number | string, emptyString = '') {
  return formatDate(time, 'YYYY-MM-DD HH:mm:ss', emptyString);
}

export function formatDateMinute(time: Date | number | string) {
  return formatDate(time, 'YYYY-MM-DD HH:mm');
}

export function isDate(value: any): value is Date {
  return value instanceof Date;
}

export function isDayjsObject(value: any): value is dayjs.Dayjs {
  return dayjs.isDayjs(value);
}
