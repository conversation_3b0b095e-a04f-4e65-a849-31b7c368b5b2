<script setup lang="ts">
import { computed } from 'vue';

import { StatusTag } from '@vben/base-ui';
import { getNestedValue } from '@vben/utils';

import { Descriptions, DescriptionsItem } from 'ant-design-vue';

interface SchemaItem {
  dictCode?: string;
  fieldName: string;
  formatter?: Function;
  label: string;
  layoutType?: string;
  slotName?: string;
  span?: number;
  schema?: SchemaItem[];
}

interface Props {
  schema: SchemaItem[];
  data: object | object[];
}

const props = defineProps<Props>();

// 判断数据是否为数组
const isDataArray = computed(() => Array.isArray(props.data));

// 获取数据数组，如果不是数组则包装成数组
const dataArray = computed(() => {
  return isDataArray.value ? (props.data as object[]) : [props.data as object];
});

const getValue = (item: Props['schema'][number], dataItem: object) => {
  return item.formatter
    ? item.formatter(getNestedValue(dataItem, item.fieldName))
    : getNestedValue(dataItem, item.fieldName);
};

const handleShow = (item: Props['schema'][number], dataItem: object) => {
  const checkShow = getNestedValue(item, 'dependencies.show');
  return checkShow ? checkShow(dataItem) : true;
};
</script>

<template>
  <div>
    <div
      v-for="(dataItem, dataIndex) in dataArray"
      :key="dataIndex"
      :class="{ 'mb-4': dataIndex !== dataArray.length - 1 }"
    >
      <Descriptions v-bind="$attrs">
        <template v-for="(item, index) in props.schema" :key="item.fieldName + index">
          <DescriptionsItem v-if="handleShow(item, dataItem)" :label="item.label" :span="item.span">
            <slot :name="item.slotName" v-bind="{ data: getValue(item, dataItem), dataItem, dataIndex }">
              <StatusTag
                v-if="item.layoutType === 'tag' && item.dictCode"
                :value="getValue(item, dataItem)"
                :code="item.dictCode"
              />
              <template v-else>
                {{ getValue(item, dataItem) }}
              </template>
            </slot>
          </DescriptionsItem>
        </template>
      </Descriptions>
    </div>
  </div>
</template>

<style></style>
