{"name": "@vben/base-ui", "version": "0.0.1", "type": "module", "sideEffects": ["**/*.css"], "imports": {"#/*": "./src/*"}, "exports": {".": {"types": "./src/index.ts", "default": "./src/index.ts"}}, "dependencies": {"@vben/stores": "workspace:*", "@vben/common-ui": "workspace:*", "@vben/types": "workspace:*", "@vben/icons": "workspace:*", "@vben/utils": "workspace:*", "@vben/fe-ui": "workspace:*", "@vben-core/shadcn-ui": "workspace:*", "@ant-design/icons-vue": "catalog:", "ant-design-vue": "catalog:", "dayjs": "catalog:", "vue3-signature": "catalog:", "vue": "catalog:", "vue-router": "catalog:", "lodash-es": "catalog:", "spark-md5": "catalog:", "vxe-table": "catalog:"}, "devDependencies": {"@types/lodash-es": "catalog:", "@types/spark-md5": "catalog:"}}