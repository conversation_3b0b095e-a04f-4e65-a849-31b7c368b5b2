import type { SchemaItem } from '../index';

export const name = '排污限期整改';
export const schema: SchemaItem[] = [
  { fieldName: 'eid', label: '企业eid', isChange: true },
  { fieldName: 'ename', label: '单位名称' },
  { fieldName: 'province', label: '省' },
  { fieldName: 'city', label: '地市' },
  { fieldName: 'area', label: '区域' },
  { fieldName: 'area_code', label: '区域代码' },
  { fieldName: 'register_no', label: '限期整改编号' },
  { fieldName: 'address', label: '地址' },
  { fieldName: 'industry', label: '行业类别' },
  { fieldName: 'valid_date', label: '有效期限' },
  { fieldName: 'issue_date', label: '下达时间' },
  { fieldName: 'detail', label: '整改详情' },
  { fieldName: 'notice_version_record', label: '整改通知书版本记录' },
  { fieldName: 'attachment', label: '附件通知书' },
];
