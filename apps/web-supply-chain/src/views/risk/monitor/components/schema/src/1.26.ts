import type { SchemaItem } from '../index';

export const name = '大股东变更';
export const schema: SchemaItem[] = [
  { fieldName: 'eid', label: '企业eid', isChange: true },
  { fieldName: 'stock_name', label: '股东名' },
  { fieldName: 'stock_type', label: '股东类型' },
  { fieldName: 'share_type', label: '股份类型' },
  { fieldName: 'stock_percent', label: '出资比例' },
  { fieldName: 'should_capi_conv', label: '认缴额（万）换算为人民币' },
  { fieldName: 'should_capi', label: '认缴额（万）' },
  { fieldName: 'real_capi', label: '实缴额（万）' },
  { fieldName: 'currency', label: '币种' },
  { fieldName: 'currency_code', label: '币种编码' },
  { fieldName: 'stock_num', label: '持股数' },
  { fieldName: 'con_date', label: '出资日期' },
  { fieldName: 'create_time', label: '记录创建时间' },
  { fieldName: 'is_history', label: '是否历史' },
  { fieldName: 'country', label: '股东国别' },
  { fieldName: 'country_code', label: '股东国别代码' },
  { fieldName: 'real_capi_date', label: '实缴出资日期' },
  { fieldName: 'stock_type_code', label: '股东类型代码' },
];
