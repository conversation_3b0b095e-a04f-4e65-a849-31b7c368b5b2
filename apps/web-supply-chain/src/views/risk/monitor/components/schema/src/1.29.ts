import type { SchemaItem } from '../index';

export const name = '社团证书有效期';
export const schema: SchemaItem[] = [
  { fieldName: 'validity_date', label: '证书有效期', isChange: true },
  { fieldName: 'currency_unit', label: '货币单位' },
  { fieldName: 'regist_capi_new', label: '注册资本数字' },
  { fieldName: 'agency', label: '登记管理机关' },
  { fieldName: 'people', label: '法定代表人' },
  { fieldName: 'org_type', label: '组织类型' },
  { fieldName: 'new_status_code', label: '登记状态码' },
  { fieldName: 'status', label: '登记状态' },
];
