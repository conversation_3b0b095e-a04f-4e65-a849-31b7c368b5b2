import type { SchemaItem } from '../index';

export const name = '终本案件';
export const schema: SchemaItem[] = [
  { fieldName: 'eid', label: '企业id', isChange: true },
  { fieldName: 'amount', label: '执行标的（元）' },
  { fieldName: 'ename', label: '公司名称' },
  { fieldName: 'card_num', label: '企业证照号' },
  { fieldName: 'case_no_origin', label: '原始案号（立案案号）' },
  { fieldName: 'case_no_terminal', label: '终本案号' },
  { fieldName: 'court', label: '执行法院' },
  { fieldName: 'fail_perform_amount', label: '未履行金额（元）' },
  { fieldName: 'filing_date', label: '立案时间' },
  { fieldName: 'sex', label: '性别' },
  { fieldName: 'terminate_date', label: '终本日期' },
];
