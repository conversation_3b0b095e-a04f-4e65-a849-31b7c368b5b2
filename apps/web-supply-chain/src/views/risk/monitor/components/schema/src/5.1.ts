import type { SchemaItem } from '../index';

export const name = '行政许可';
export const schema: SchemaItem[] = [
  { fieldName: 'name', label: '许可文件名称', isChange: true },
  { fieldName: 'content', label: '许可内容' },
  { fieldName: 'department', label: '许可机关' },
  { fieldName: 'eid', label: '企业eid' },
  { fieldName: 'end_date', label: '有效期至' },
  { fieldName: 'number', label: '许可文件编号' },
  { fieldName: 'start_date', label: '有效期自' },
  { fieldName: 'status', label: '状态' },
  { fieldName: 'status_code', label: '状态码值' },
];
