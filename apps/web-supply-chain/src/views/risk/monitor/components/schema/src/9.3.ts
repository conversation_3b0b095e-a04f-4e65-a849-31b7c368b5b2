import type { SchemaItem } from '../index';

export const name = '违规处理';
export const schema: SchemaItem[] = [
  { fieldName: 'eid', label: '企业eid', isChange: true },
  { fieldName: 'noticedate', label: '公告日期' },
  { fieldName: 'securityname', label: '证券名称' },
  { fieldName: 'gooltype', label: '违规类型' },
  { fieldName: 'goolsubject', label: '违规主体' },
  { fieldName: 'punishobject', label: '处罚对象' },
  { fieldName: 'punishobjecteid', label: '处罚对象eid' },
  { fieldName: 'title', label: '文件标题' },
  { fieldName: 'goolaction', label: '违规行为' },
  { fieldName: 'punishtype', label: '处分类型' },
  { fieldName: 'relation', label: '关系' },
  { fieldName: 'punishstep', label: '处分措施' },
  { fieldName: 'handleperson', label: '处理人' },
  { fieldName: 'punishjine', label: '处罚金额(万元)' },
  { fieldName: 'interenactment', label: '相关法规' },
  { fieldName: 'isverified', label: '是否得到明确结论' },
  { fieldName: 'eventid', label: '事件ID' },
  { fieldName: 'punishdate', label: '处罚时间' },
  { fieldName: 'refnumber', label: '处罚文号' },
  { fieldName: 'handlepercode', label: '处理人代码' },
  { fieldName: 'infosource', label: '信息来源' },
  { fieldName: 'stage', label: '发生阶段' },
  { fieldName: 'punishobjectcode', label: '处罚对象代码' },
];
