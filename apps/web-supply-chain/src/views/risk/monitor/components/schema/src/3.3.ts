import type { SchemaItem } from '../index';

export const name = '法院公告';
export const schema: SchemaItem[] = [
  { fieldName: 'role', label: '当事人身份', isChange: true },
  { fieldName: 'content', label: '详情' },
  { fieldName: 'court', label: '公告人/法院' },
  { fieldName: 'eid', label: '企业id' },
  { fieldName: 'ename', label: '企业名称' },
  { fieldName: 'date', label: '公告时间' },
  { fieldName: 'people', label: '当事人' },
  { fieldName: 'type', label: '公告类型' },
];
