import type { SchemaItem } from '../index';

export const name = '企业公告';
export const schema: SchemaItem[] = [
  { fieldName: 'eid', label: '企业id', isChange: true },
  { fieldName: 'infocode', label: '资讯编码' },
  { fieldName: 'sourcename', label: '来源名称' },
  { fieldName: 'publishtype', label: '发布主体' },
  { fieldName: 'noticedate', label: '公告日期' },
  { fieldName: 'enddate', label: '截止日期' },
  { fieldName: 'noticetitle', label: '公告标题' },
  { fieldName: 'publishdate', label: '公告发布时间' },
  { fieldName: 'language', label: '语言类别' },
  { fieldName: 'importlevel', label: '重要性等级' },
  { fieldName: 'form', label: '美股披露公告类型' },
  { fieldName: 'noticestate', label: '公告状态' },
  { fieldName: 'noticetitlet', label: '公告标题' },
  { fieldName: 'columncode', label: '栏目编码' },
  { fieldName: 'columnname', label: '栏目名称' },
  { fieldName: 'columnqxbcode', label: '公告标签' },
  { fieldName: 'is_bond', label: '是否为债券' },
  { fieldName: 'secinnercode', label: '证券内码' },
  { fieldName: 'securitytype', label: '证券类型' },
  { fieldName: 'trademarket', label: '交易市场' },
  { fieldName: 'securitycode', label: '证券代码' },
  { fieldName: 'ename', label: '企业名称' },
];
