import type { SchemaItem } from '../index';

export const name = '融资动态';
export const schema: SchemaItem[] = [
  { fieldName: 'round', label: '轮次', isChange: true },
  { fieldName: 'amount', label: '投资金额' },
  { fieldName: 'currency', label: '币种' },
  { fieldName: 'date', label: '融资日期' },
  { fieldName: 'eid', label: '企业eid' },
  { fieldName: 'investors', label: '本轮投资人' },
  { fieldName: 'precise', label: '准确性' },
  { fieldName: 'publish_date', label: '发布日期' },
];
