import type { SchemaItem } from '../index';

export const name = '司法协助';
export const schema: SchemaItem[] = [
  { fieldName: 'type_code', label: '类型编码', isChange: true },
  { fieldName: 'amount', label: '股权金额' },
  { fieldName: 'currency', label: '股权金额单位' },
  { fieldName: 'be_executed_person', label: '被执行人' },
  { fieldName: 'number', label: '协助公示通知书文号' },
  { fieldName: 'status', label: '状态' },
  { fieldName: 'status_code', label: '状态代码' },
  { fieldName: 'amount_new', label: '冻结金额新' },
];
