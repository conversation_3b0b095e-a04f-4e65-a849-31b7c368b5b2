import type { SchemaItem } from '../index';

export const name = '立案信息';
export const schema: SchemaItem[] = [
  { fieldName: 'role', label: '角色', isChange: true },
  { fieldName: 'agent', label: '承办人' },
  { fieldName: 'assistant', label: '助理法官' },
  { fieldName: 'case_no', label: '案号' },
  { fieldName: 'case_status', label: '案件状态' },
  { fieldName: 'end_date', label: '结束时间' },
  { fieldName: 'hearing_date', label: '开庭日期' },
  { fieldName: 'region', label: '地区' },
  { fieldName: 'start_date', label: '立案时间' },
  { fieldName: 'ename', label: '企业名称' },
  { fieldName: 'eid', label: '企业eid' },
  { fieldName: 'regist_capi', label: '注册资本' },
];
