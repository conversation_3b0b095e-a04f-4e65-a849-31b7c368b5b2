import type { SchemaItem } from '../index';

export const name = '严重违法';
export const schema: SchemaItem[] = [
  { fieldName: 'ill_type', label: '违法类型', span: 2 },
  { fieldName: 'in_department', label: '移入部门' },
  { fieldName: 'in_date', label: '移入时间' },
  { fieldName: 'in_reason', label: '移入原因', span: 2 },
  { fieldName: 'out_department', label: '移除部门' },
  { fieldName: 'out_date', label: '移除时间' },
  { fieldName: 'out_reason', label: '移除原因', span: 2 },
];
export const handleKeys = {
  execution: (value: string) => {
    return value ? JSON.parse(value) : {};
  },
};
export const valueKey = 'execution';
