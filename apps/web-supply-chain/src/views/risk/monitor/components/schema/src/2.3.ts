import type { SchemaItem } from '../index';

export const name = '股权出质';
export const schema: SchemaItem[] = [
  { fieldName: 'status_code', label: '状态代码', isChange: true },
  { fieldName: 'number', label: '登记编号' },
  { fieldName: 'date', label: '股权出质设立登记日期' },
  { fieldName: 'pledgor', label: '出质人' },
  { fieldName: 'pawnee', label: '质权人' },
  { fieldName: 'pledgor_amount', label: '出质股权数额' },
  { fieldName: 'pledgor_unit', label: '出质股权数额货币单位' },
  { fieldName: 'pledgor_currency', label: '出质股权数额币种' },
  { fieldName: 'status', label: '状态' },
  { fieldName: 'remark', label: '备注' },
  { fieldName: 'cancel_date', label: '注销日期' },
  { fieldName: 'cancel_content', label: '注销原因' },
  { fieldName: 'public_date', label: '公示日期' },
  { fieldName: 'object_company', label: '标的方' },
  { fieldName: 'eid', label: '企业id' },
  { fieldName: 'pledgor_eid', label: '出质人eid' },
  { fieldName: 'pawnee_eid', label: '质权人eid' },
  { fieldName: 'object_company_eid', label: '标的方企业eid' },
];
