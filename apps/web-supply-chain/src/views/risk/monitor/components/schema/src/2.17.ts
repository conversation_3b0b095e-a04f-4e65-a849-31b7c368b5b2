import type { SchemaItem } from '../index';

export const name = '询价评估';
export const schema: SchemaItem[] = [
  { fieldName: 'ename', label: '企业名称', isChange: true },
  { fieldName: 'eid', label: '企业eid', isChange: true },
  { fieldName: 'case_no', label: '案号' },
  { fieldName: 'court_name', label: '法院名称' },
  { fieldName: 'property_kind', label: '财产类型' },
  { fieldName: 'property_kind_code', label: '财产类型代码' },
  { fieldName: 'subject_matter', label: '标的物名称' },
  { fieldName: 'determine_style_code', label: '确定参考价方式代码' },
  { fieldName: 'determine_style', label: '确定参考价方式' },
  { fieldName: 'pdf', label: '询价结果公示' },
  { fieldName: 'content', label: '询价结果正文' },
  { fieldName: 'case_cause', label: '案由' },
  { fieldName: 'pub_date', label: '发布日期' },
  { fieldName: 'lottery_time', label: '摇号时间' },
  { fieldName: 'evaluation_institution', label: '选定评估机构公示' },
  { fieldName: 'type', label: '类型' },
];
