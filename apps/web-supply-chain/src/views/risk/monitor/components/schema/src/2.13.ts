import type { SchemaItem } from '../index';

export const name = '注销备案';
export const schema: SchemaItem[] = [
  { fieldName: 'status', label: '状态', isChange: true },
  { fieldName: 'credit_no', label: '统一社会信用代码/注册号' },
  { fieldName: 'belong_org', label: '登记机关' },
  { fieldName: 'audit_reg_date', label: '清算组备案日期' },
  { fieldName: 'audit_start_date', label: '清算组成立日期' },
  { fieldName: 'logout_reason', label: '决议解散' },
  { fieldName: 'audit_address', label: '清算组办公地址' },
  { fieldName: 'audit_phone', label: '清算组联系电话' },
  { fieldName: 'audit_leader', label: '清算组负责人' },
  { fieldName: 'audit_employees', label: '清算组成员' },
  { fieldName: 'creditor_start_date', label: '债权人公告开始日期' },
  { fieldName: 'creditor_end_date', label: '债权人公告结束日期' },
  { fieldName: 'creditor_announcement', label: '公告内容' },
  { fieldName: 'creditor_person', label: '债权申报联系人' },
  { fieldName: 'creditor_phone', label: '债权申报联系电话' },
  { fieldName: 'creditor_address', label: '债权申报地址' },
];
