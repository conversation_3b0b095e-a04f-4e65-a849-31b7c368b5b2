import type { SchemaItem } from '../index';

export const name = '参保人数变更';
export const schema: SchemaItem[] = [
  { fieldName: 'yiliaobx_num', label: '职工基本医疗保险人数', isChange: true },
  { fieldName: 'report_year', label: '年报年份' },
  { fieldName: 'report_name', label: '年报名称' },
  { fieldName: 'report_date', label: '年报日期' },
  { fieldName: 'shiyebx_num', label: '失业保险人数' },
  { fieldName: 'shengyubx_num', label: '生育保险人数' },
  { fieldName: 'gongshangbx_num', label: '工伤保险人数' },
  { fieldName: 'yanglaobx_num', label: '城镇职工基本养老保险人数' },
  { fieldName: 'bq_shengyubx_je', label: '参加生育保险本期实际缴费金额' },
  { fieldName: 'bq_shiyebx_je', label: '参加失业保险本期实际缴费金额' },
  { fieldName: 'bq_yiliaobx_je', label: '参加职工基本医疗保险本期实际缴费金额' },
  { fieldName: 'bq_gongshangbx_je', label: '参加工伤保险本期实际缴费金额' },
  { fieldName: 'bq_yanglaobx_je', label: '参加城镇职工基本养老保险本期实际缴费金额' },
  { fieldName: 'dw_yanglaobx_je', label: '单位参加城镇职工基本养老保险累计欠缴金额' },
  { fieldName: 'dw_yiliaobx_je', label: '单位参加职工基本医疗保险累计欠缴金额' },
  { fieldName: 'dw_shengyubx_je', label: '单位参加生育保险累计欠缴金额' },
  { fieldName: 'dw_gongshangbx_je', label: '单位参加工伤保险累计欠缴金额' },
  { fieldName: 'dw_shiyebx_je', label: '单位参加失业保险累计欠缴金额' },
  { fieldName: 'dw_yanglaobx_js', label: '单位参加城镇职工基本养老保险缴费基数' },
  { fieldName: 'dw_gongshangbx_js', label: '单位参加工伤保险缴费基数' },
  { fieldName: 'dw_yiliaobx_js', label: '单位参加职工基本医疗保险缴费基数' },
  { fieldName: 'dw_shengyubx_js', label: '单位参加生育保险缴费基数' },
  { fieldName: 'dw_shiyebx_js', label: '单位参加失业保险缴费基数' },
  { fieldName: 'currency', label: '币种' },
  { fieldName: 'created_time', label: '创建时间' },
];
