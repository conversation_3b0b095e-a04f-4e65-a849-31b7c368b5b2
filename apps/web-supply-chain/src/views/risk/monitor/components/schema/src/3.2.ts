import type { SchemaItem } from '../index';

export const name = '开庭公告';
export const schema: SchemaItem[] = [
  { fieldName: 'pure_role', label: '当事人身份', isChange: true },
  { fieldName: 'case_no', label: '案号' },
  { fieldName: 'cause_action', label: '案由' },
  { fieldName: 'court', label: '法院' },
  { fieldName: 'ename', label: '企业名称' },
  { fieldName: 'eid', label: '企业id' },
  { fieldName: 'department', label: '承办部门' },
  { fieldName: 'hearing_date', label: '开庭日期' },
  { fieldName: 'judge', label: '审判长' },
  { fieldName: 'title', label: '标题' },
  { fieldName: 'tribunal', label: '法庭' },
];
