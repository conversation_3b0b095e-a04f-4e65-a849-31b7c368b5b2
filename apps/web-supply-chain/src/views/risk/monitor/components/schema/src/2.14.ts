import type { SchemaItem } from '../index';

export const name = '债务人信息';
export const schema: SchemaItem[] = [
  { fieldName: 'eid', label: '债务人eid', isChange: true },
  { fieldName: 'name', label: '债务人名称' },
  { fieldName: 'industry', label: '行业' },
  { fieldName: 'region', label: '注册地' },
  { fieldName: 'assets', label: '主要资产' },
  { fieldName: 'employees', label: '职工人数' },
  { fieldName: 'mechanism', label: '管理人机构' },
  { fieldName: 'mechanism_eid', label: '管理人机构eid' },
  { fieldName: 'principal_name', label: '管理人' },
];
