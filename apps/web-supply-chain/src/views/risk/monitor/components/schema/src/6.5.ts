import type { SchemaItem } from '../index';

export const name = '域名信息';
export const schema: SchemaItem[] = [
  { fieldName: 'home_url', label: '网站首页网址', isChange: true },
  { fieldName: 'province', label: '省份' },
  { fieldName: 'domain', label: '网站域名' },
  { fieldName: 'site_name', label: '网站名称' },
  { fieldName: 'check_date', label: '登记批准时间' },
  { fieldName: 'number', label: '网站备案许可证号' },
  { fieldName: 'city', label: '备案城市' },
];
