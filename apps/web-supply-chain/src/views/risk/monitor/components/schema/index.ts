export interface SchemaItem {
  dictCode?: string;
  fieldName: string;
  formatter?: (value: any) => string;
  label: string;
  layoutType?: string;
  slotName?: string;
  span?: number;
  isChange?: boolean;
  schema?: SchemaItem[];
}

export const schemaList: {
  handleKeys?: { [key: string]: (value: string) => any };
  name: string;
  schema: SchemaItem[];
  valueKey?: string;
}[] = [];

const modules = import.meta.glob('./src/*.ts');

for (const importFn of Object.values(modules)) {
  importFn().then((module: any) => {
    if (module.name && module.schema) {
      schemaList.push({
        name: module.name,
        schema: module.schema,
        handleKeys: module.handleKeys,
        valueKey: module.valueKey,
      });
    }
  });
}
