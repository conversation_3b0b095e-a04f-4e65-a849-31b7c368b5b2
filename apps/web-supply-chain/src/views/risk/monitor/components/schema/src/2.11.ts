import type { SchemaItem } from '../index';

export const name = '破产案件';
export const schema: SchemaItem[] = [
  { fieldName: 'eid', label: '被申请人eid', isChange: true },
  { fieldName: 'app_name', label: '申请人姓名' },
  { fieldName: 'case_kind', label: '案件类型' },
  { fieldName: 'name', label: '被申请人' },
  { fieldName: 'management_agency', label: '管理人机构' },
  { fieldName: 'management_agency_eid', label: '管理人机构eid' },
  { fieldName: 'court', label: '办理法院' },
  { fieldName: 'case_no', label: '案件号' },
  { fieldName: 'agency_principal', label: '管理人主要负责人' },
  { fieldName: 'pub_date', label: '公开时间' },
];
