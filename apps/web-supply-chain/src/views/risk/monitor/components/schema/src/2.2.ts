import type { SchemaItem } from '../index';

export const name = '动产抵押';
export const schema: SchemaItem[] = [
  { fieldName: 'status', label: '状态', isChange: true },
  { fieldName: 'number', label: '登记编号' },
  { fieldName: 'date', label: '登记日期' },
  { fieldName: 'amount_new', label: '被担保债权数额' },
  { fieldName: 'currency', label: '币种' },
  { fieldName: 'type', label: '被担保债权种类' },
  { fieldName: 'department', label: '登记机关' },
  { fieldName: 'close_date', label: '注销日期' },
  { fieldName: 'close_reason', label: '注销原因' },
  { fieldName: 'public_date', label: '公示日期' },
  { fieldName: 'period', label: '债务人履行债务的期限' },
  { fieldName: 'period_start', label: '开始时间' },
  { fieldName: 'period_end', label: '结束时间' },
  { fieldName: 'scope', label: '范围' },
  { fieldName: 'remarks', label: '备注' },
  { fieldName: 'debit_currency', label: '被担保债权概况-数额' },
  { fieldName: 'debit_type', label: '被担保债权概况-种类' },
  { fieldName: 'debit_amount', label: '被担保债权概况-数额' },
  { fieldName: 'debit_period', label: '被担保债权概况-债务人履行债务的期限' },
  { fieldName: 'debit_scope', label: '被担保债权概况-担保的范围' },
  { fieldName: 'debit_remarks', label: '被担保债权概况-备注' },
];
