import type { SchemaItem } from '../index';

export const name = '疑似实控人';
export const schema: SchemaItem[] = [
  { fieldName: 'entity_eid', label: '实际控制人id', isChange: true },
  { fieldName: 'eid', label: '企业eid' },
  { fieldName: 'name', label: '企业名称' },
  { fieldName: 'total_percent', label: '实控人总持股' },
  { fieldName: 'entity_name', label: '实控人名称' },
  { fieldName: 'entity_type', label: '实控人类型' },
];
