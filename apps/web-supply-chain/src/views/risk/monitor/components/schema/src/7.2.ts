import type { SchemaItem } from '../index';

export const name = '非正常户';
export const schema: SchemaItem[] = [
  { fieldName: 'state', label: '纳税人状态', isChange: true },
  { fieldName: 'area', label: '生产经营地址' },
  { fieldName: 'certificates_type', label: '证件种类' },
  { fieldName: 'judge_area', label: '认定单位的地址' },
  { fieldName: 'judge_date', label: '认定日期' },
  { fieldName: 'judge_phone', label: '认定单位的电话' },
  { fieldName: 'legal_rep', label: '法定代表人姓名' },
  { fieldName: 'name', label: '企业名称' },
  { fieldName: 'overdue_type', label: '欠税税务种类' },
  { fieldName: 'pub_date', label: '公告日期' },
  { fieldName: 'reason', label: '认定原因' },
  { fieldName: 'tax_num', label: '纳税人识别号' },
  { fieldName: 'overdue_amount', label: '欠税金额' },
];
