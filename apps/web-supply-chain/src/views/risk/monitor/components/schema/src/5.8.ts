import type { SchemaItem } from '../index';

export const name = '专精特新';
export const schema: SchemaItem[] = [
  { fieldName: 'state_code', label: '许可证状态码', isChange: true },
  { fieldName: 'district', label: '地区' },
  { fieldName: 'district_code', label: '区域代码' },
  { fieldName: 'year', label: '年份' },
  { fieldName: 'publish_date', label: '发布日期' },
  { fieldName: 'check_date', label: '复核日期' },
  { fieldName: 'end_date', label: '资质终止日期' },
  { fieldName: 'valid_start', label: '有效期起' },
  { fieldName: 'valid_end', label: '有效期至' },
  { fieldName: 'state', label: '许可证状态' },
  { fieldName: 'level', label: '级别' },
];
