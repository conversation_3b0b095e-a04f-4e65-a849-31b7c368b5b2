import type { SchemaItem } from '../index';

export const name = '劳动仲裁送达公告';
export const schema: SchemaItem[] = [
  { fieldName: 'eid', label: '企业id', isChange: true },
  { fieldName: 'role', label: '角色' },
  { fieldName: 'related_companies', label: '关联企业/人员' },
  { fieldName: 'case_no', label: '案号' },
  { fieldName: 'doc_id', label: '仲裁编号' },
  { fieldName: 'date', label: '公告时间' },
  { fieldName: 'department', label: '公告人' },
  { fieldName: 'title', label: '标题' },
  { fieldName: 'content', label: '公告详情' },
  { fieldName: 'case_reason', label: '案由' },
];
