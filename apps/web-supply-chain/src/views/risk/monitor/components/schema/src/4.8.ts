import type { SchemaItem } from '../index';

export const name = '劳动仲裁开庭公告';
export const schema: SchemaItem[] = [
  { fieldName: 'eid', label: '企业id', isChange: true },
  { fieldName: 'role', label: '角色' },
  { fieldName: 'related_companies', label: '关联企业/人员' },
  { fieldName: 'case_no', label: '案号' },
  { fieldName: 'doc_id', label: '仲裁编号' },
  { fieldName: 'hearing_date', label: '开庭时间' },
  { fieldName: 'hearing_venue', label: '开庭地点' },
  { fieldName: 'title', label: '标题' },
  { fieldName: 'release_time', label: '发布日期' },
  { fieldName: 'case_reason', label: '案由' },
  { fieldName: 'arbitrators', label: '仲裁员' },
  { fieldName: 'clerk', label: '书记员' },
  { fieldName: 'department', label: '发布机构' },
];
