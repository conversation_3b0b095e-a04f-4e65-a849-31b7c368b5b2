import type { SchemaItem } from '../index';

export const name = '资质信息';
export const schema: SchemaItem[] = [
  { fieldName: 'eid', label: '企业id', isChange: true },
  { fieldName: 'ename', label: '企业名称' },
  { fieldName: 'register_no', label: '证书编号' },
  { fieldName: 'type', label: '证书类型' },
  { fieldName: 'valid_start', label: '发证日期' },
  { fieldName: 'valid_end', label: '有效期至' },
  { fieldName: 'state', label: '状态' },
  { fieldName: 'table_name', label: '分表名称' },
  { fieldName: 'license_name', label: '证书名称' },
];
