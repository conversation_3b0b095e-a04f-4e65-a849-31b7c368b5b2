import type { SchemaItem } from '../index';

export const name = '黑名单移入';
export const schema: SchemaItem[] = [
  { fieldName: 'in_lists_date', label: '列入黑名单日期', isChange: true },
  { fieldName: 'lists_type', label: '黑名单类型' },
  { fieldName: 'department_level', label: '认定部门等级' },
  { fieldName: 'credit_no', label: '统一社会信用代码' },
  { fieldName: 'black_basis', label: '黑名单认定依据' },
  { fieldName: 'maintain_department', label: '认定部门' },
  { fieldName: 'out_lists_date', label: '移出黑名单日期' },
  { fieldName: 'details', label: '失信情况' },
  { fieldName: 'punishment_result', label: '处罚结果' },
];
