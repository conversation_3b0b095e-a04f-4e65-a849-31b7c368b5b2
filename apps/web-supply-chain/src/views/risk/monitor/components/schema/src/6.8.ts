import type { SchemaItem } from '../index';

export const name = '商标转让';
export const schema: SchemaItem[] = [
  { fieldName: 'eid', label: '企业eid', isChange: true },
  { fieldName: 'role', label: '角色' },
  { fieldName: 'reg_number', label: '申请/注册号' },
  { fieldName: 'type_num', label: '国际分类' },
  { fieldName: 'notice_no', label: '公告期号' },
  { fieldName: 'notice_type', label: '公告类型' },
  { fieldName: 'notice_date', label: '公告日期' },
  { fieldName: 'notice_content', label: '公告内容' },
  { fieldName: 'assignee', label: '商标受让人' },
  { fieldName: 'transferor', label: '商标转让人' },
  { fieldName: 'trademark_name', label: '商标名称' },
];
