import type { SchemaItem } from '../index';

export const name = '行政处罚';
export const schema: SchemaItem[] = [
  { fieldName: 'number', label: '处罚编号', isChange: true },
  { fieldName: 'tags', label: '历史信息标记', isChange: true },
  { fieldName: 'date', label: '处罚决定日期/处罚生效期' },
  { fieldName: 'department', label: '处罚机关' },
  { fieldName: 'based_on', label: '处罚依据' },
  { fieldName: 'content', label: '处罚结果' },
  { fieldName: 'description', label: '描述' },
  { fieldName: 'illegal_type', label: '违法类型' },
  { fieldName: 'publish_date', label: '公示日期' },
  { fieldName: 'punish_amnt', label: '罚款金额（万元）' },
  { fieldName: 'status', label: '状态' },
  { fieldName: 'push_type_one', label: '处罚类别1' },
  { fieldName: 'reason', label: '处罚事由' },
  { fieldName: 'illegal_income_amnt', label: '违法所得金额' },
];
