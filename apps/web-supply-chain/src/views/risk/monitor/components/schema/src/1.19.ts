import type { SchemaItem } from '../index';

export const name = '经营期限日期变更';
export const schema: SchemaItem[] = [
  { fieldName: 'term_start', label: '经营期限起始日期', isChange: true },
  { fieldName: 'term_end', label: '经营期限结束日期', isChange: true },
  { fieldName: 'ename', label: '公司名称' },
  { fieldName: 'regist_capi_new', label: '注册资本(新)（万元）' },
  { fieldName: 'currency_unit', label: '货币单位' },
  { fieldName: 'econ_kind', label: '企业类型' },
  { fieldName: 'econ_kind_code', label: '企业类型代码' },
  { fieldName: 'oper_name', label: '法定代表人' },
  { fieldName: 'scope', label: '经营范围' },
  { fieldName: 'belong_org', label: '所属工商局' },
  { fieldName: 'check_date', label: '核准日期' },
  { fieldName: 'new_status_code', label: '企业状态码' },
  { fieldName: 'name', label: '企业名称' },
];
