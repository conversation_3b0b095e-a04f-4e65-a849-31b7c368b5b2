import type { SchemaItem } from '../index';

export const name = '股权冻结';
export const schema: SchemaItem[] = [
  { fieldName: 'amount_new', label: '冻结金额新', isChange: true },
  { fieldName: 'status_code', label: '状态代码', isChange: true },
  { fieldName: 'amount', label: '股权金额' },
  { fieldName: 'currency', label: '股权金额单位' },
  { fieldName: 'be_executed_person', label: '被执行人' },
  { fieldName: 'number', label: '协助公示通知书文号' },
  { fieldName: 'status', label: '状态' },
  { fieldName: 'detail_freeze_start_date', label: '冻结起始日期' },
  { fieldName: 'ename', label: '企业名称' },
  { fieldName: 'detail_public_date', label: '发布日期' },
  { fieldName: 'type_code', label: '类型编码' },
];
