import type { SchemaItem } from '../index';

export const name = '失信人';
export const schema: SchemaItem[] = [
  { fieldName: 'execution_status', label: '被执行人的履行情况', isChange: true },
  { fieldName: 'amount', label: '标的（元）' },
  { fieldName: 'case_number', label: '案号' },
  { fieldName: 'date', label: '立案时间' },
  { fieldName: 'doc_number', label: '执行依据文号' },
  { fieldName: 'eid', label: '企业id' },
  { fieldName: 'publish_date', label: '公布日期' },
  { fieldName: 'ex_department', label: '做出执行依据单位' },
  { fieldName: 'oper_name', label: '法人名称' },
  { fieldName: 'province', label: '省份' },
  { fieldName: 'execution_desc', label: '失信被执行人具体行为情形' },
  { fieldName: 'final_duty', label: '生效法律文书确定的义务' },
  { fieldName: 'sex', label: '性别' },
  { fieldName: 'age', label: '年龄' },
  { fieldName: 'number', label: '企业证照号' },
  { fieldName: 'type', label: '当事人类型' },
  { fieldName: 'concern_count', label: '关注数' },
  { fieldName: 'import_date', label: '导入事件' },
  { fieldName: 'status', label: '状态' },
  { fieldName: 'case_relation', label: '是否有案件关联' },
  { fieldName: 'court', label: '法院' },
];
