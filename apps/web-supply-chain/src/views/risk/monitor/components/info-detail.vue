<script setup lang="ts">
import type { MonitorCompanyInfo } from '#/api';

import { ref, watch } from 'vue';

import { DynamicDescriptions } from '@vben/base-ui';
import { DESCRIPTIONS_PROP } from '@vben/constants';

import { Descriptions, DescriptionsItem } from 'ant-design-vue';

import { schemaList } from '#/views/risk/monitor/components/schema';

const props = defineProps({
  detail: {
    type: Object,
    required: true,
  },
});
const originalData = ref<MonitorCompanyInfo>({});
const infoData = ref({});
const schema = ref<{ fieldName: string; label: string }[]>([]);
const init = (detail: MonitorCompanyInfo) => {
  console.log(detail);
  let changeData: { [key: string]: any } = {};
  originalData.value = detail;
  const typeConfig = schemaList.find((item) => item.name === detail.changeTableName);
  schema.value = typeConfig?.schema ?? [];
  if (detail.changeAfter) {
    changeData = JSON.parse(detail.changeAfter);
  } else if (detail.changeBefore) {
    changeData = JSON.parse(detail.changeBefore);
  }
  const handleKeys = typeConfig?.handleKeys ?? {};
  console.log(handleKeys);
  Object.keys(changeData).forEach((key: string) => {
    if (handleKeys[key]) {
      changeData[key] = handleKeys[key](changeData[key]);
    }
  });
  const valueKey = typeConfig?.valueKey;
  if (valueKey) {
    changeData = changeData[valueKey];
  } else {
    infoData.value = changeData;
  }
  console.log(infoData.value);
};
watch(() => props.detail, init, {
  immediate: true,
});
</script>

<template>
  <div>
    <Descriptions v-bind="DESCRIPTIONS_PROP" class="mb-4" title="基本信息">
      <DescriptionsItem label="公司名称">{{ originalData.name }}</DescriptionsItem>
      <DescriptionsItem label="社会信用代码">{{ originalData.creditNo }}</DescriptionsItem>
      <DescriptionsItem label="变更维度">{{ originalData.changeTableName }}</DescriptionsItem>
      <DescriptionsItem label="变更类型">{{ originalData.changeType }}</DescriptionsItem>
    </Descriptions>
    <DynamicDescriptions :data="infoData" :schema="schema" v-bind="DESCRIPTIONS_PROP" title="变更信息" />
  </div>
</template>

<style></style>
